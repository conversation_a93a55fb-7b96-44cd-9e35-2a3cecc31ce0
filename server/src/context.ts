import type { CreateFastifyContextOptions } from '@trpc/server/adapters/fastify'
import { db } from './db'
import { verifyToken } from './lib/jwt'
import type { User } from './db/user'
import { users } from './db/user'
import { eq } from 'drizzle-orm'

const createContext = async ({ req, res }: CreateFastifyContextOptions) => {
  let user: User | null = null

  const token = req.headers.authorization?.split(' ')[1]
  if (token) {
    try {
      const decoded = verifyToken(token)
      const foundUser = await db.query.users.findFirst({
        where: eq(users.id, decoded.userId),
      })
      if (foundUser) {
        user = foundUser
      }
    } catch {
      // Token 验证失败，不设置用户信息
    }
  }

  return { req, res, db, user }
}

export type Context = Awaited<ReturnType<typeof createContext>>

export default createContext
