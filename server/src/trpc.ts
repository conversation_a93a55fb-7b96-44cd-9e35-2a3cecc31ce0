import { initTRPC, TRPCError } from '@trpc/server'
import { ZodError } from 'zod'
import type { OpenApiMeta } from 'trpc-to-openapi'
import superjson from 'superjson'
import type { Context } from './context'
import { verifyToken } from './lib/jwt'
import type { JWTPayload } from './lib/jwt'
import { users } from './db/schema'
import { eq } from 'drizzle-orm'
import { env } from './env'

const t = initTRPC
  .meta<OpenApiMeta>()
  .context<Context>()
  .create({
    transformer: superjson,
    errorFormatter(opts) {
      const { shape, error } = opts
      if (error.code === 'INTERNAL_SERVER_ERROR' && env.NODE_ENV === 'production') {
        return { ...shape, message: 'Internal server error' }
      }
      return {
        ...shape,
        data: {
          ...shape.data,
          zodError: error.code === 'BAD_REQUEST' && error.cause instanceof ZodError ? error.cause.flatten() : null,
        },
      }
    },
  })

export default t

export const publicProcedure = t.procedure
export const router = t.router

// 认证中间件
const authMiddleware = t.middleware(async ({ ctx, next }) => {
  // 从cookie或Authorization header获取token
  const token =
    ctx.req.cookies['auth-token'] ??
    (ctx.req.headers.authorization?.startsWith('Bearer ') ? ctx.req.headers.authorization.slice(7) : undefined)

  if (!token) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: '请先登录',
    })
  }

  try {
    const payload: JWTPayload = verifyToken(token)

    // 从数据库获取用户信息
    // const dbUser = await ctx.db.select().from(users).where(eq(users.id, payload.userId)).limit(1)
    const dbUser = await ctx.db.query.users.findFirst({
      where: eq(users.id, payload.userId),
    })

    if (!dbUser) {
      throw new TRPCError({
        code: 'UNAUTHORIZED',
        message: '用户不存在',
      })
    }

    return next({
      ctx: {
        ...ctx,
        user: dbUser, // 使用schema定义的User类型
      },
    })
  } catch (error) {
    if (error instanceof TRPCError) {
      throw error
    }

    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'Token无效或已过期',
    })
  }
})

// 受保护的程序，需要用户登录
export const protectedProcedure = t.procedure.use(authMiddleware)
