import { TRPCError } from '@trpc/server'
import { z } from 'zod'
import { protectedProcedure, router } from '@/trpc'
import { applications, CreateApplicationSchema, UpdateApplicationSchema, ApplicationListSchema } from '@/db/schema'
import { eq, and, desc, count, ilike } from 'drizzle-orm'
import { generateUniqueAppId, generateSecret } from '@/lib/application'

export const applicationRouter = router({
  // 获取应用列表（分页）
  list: protectedProcedure.input(ApplicationListSchema).query(async ({ ctx, input }) => {
    const { page, pageSize, search } = input
    const offset = (page - 1) * pageSize

    // 构建查询条件
    const whereConditions = [eq(applications.userId, ctx.user.id)]

    if (search) {
      whereConditions.push(ilike(applications.name, `%${search}%`))
    }

    // 获取总数
    const [totalResult] = await ctx.db
      .select({ count: count() })
      .from(applications)
      .where(and(...whereConditions))

    const total = totalResult?.count || 0

    // 获取分页数据
    const items = await ctx.db.query.applications.findMany({
      where: and(...whereConditions),
      orderBy: desc(applications.createdAt),
      limit: pageSize,
      offset,
    })

    return {
      items,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    }
  }),

  // 创建应用
  create: protectedProcedure.input(CreateApplicationSchema).mutation(async ({ ctx, input }) => {
    // 生成唯一的应用ID和密钥
    const appId = await generateUniqueAppId()
    const secret = generateSecret()

    const newApplication = await ctx.db
      .insert(applications)
      .values({
        ...input,
        appId,
        secret,
        userId: ctx.user.id,
      })
      .returning()

    if (!newApplication[0]) {
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: '创建应用失败',
      })
    }

    return newApplication[0]
  }),

  // 获取单个应用详情
  byId: protectedProcedure.input(z.object({ id: z.string().uuid() })).query(async ({ ctx, input }) => {
    const application = await ctx.db.query.applications.findFirst({
      where: and(eq(applications.id, input.id), eq(applications.userId, ctx.user.id)),
    })

    if (!application) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: '应用不存在',
      })
    }

    return application
  }),

  // 更新应用
  update: protectedProcedure
    .input(
      z
        .object({
          id: z.string().uuid(),
        })
        .merge(UpdateApplicationSchema)
    )
    .mutation(async ({ ctx, input }) => {
      const { id, ...updateData } = input

      // 检查应用是否存在且属于当前用户
      const existingApp = await ctx.db.query.applications.findFirst({
        where: and(eq(applications.id, id), eq(applications.userId, ctx.user.id)),
      })

      if (!existingApp) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '应用不存在',
        })
      }

      // 只更新允许的字段
      const filteredUpdateData = {
        ...(updateData.name && { name: updateData.name }),
        ...(updateData.description !== undefined && { description: updateData.description }),
      }

      const updated = await ctx.db
        .update(applications)
        .set(filteredUpdateData)
        .where(eq(applications.id, id))
        .returning()

      if (!updated[0]) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '更新应用失败',
        })
      }

      return updated[0]
    }),

  // 删除应用
  delete: protectedProcedure.input(z.object({ id: z.string().uuid() })).mutation(async ({ ctx, input }) => {
    // 检查应用是否存在且属于当前用户
    const existingApp = await ctx.db.query.applications.findFirst({
      where: and(eq(applications.id, input.id), eq(applications.userId, ctx.user.id)),
    })

    if (!existingApp) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: '应用不存在',
      })
    }

    await ctx.db.delete(applications).where(eq(applications.id, input.id))

    return { success: true }
  }),
})
