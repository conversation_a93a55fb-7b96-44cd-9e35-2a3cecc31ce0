import { TRPCError } from '@trpc/server'
import { z } from 'zod'
import { protectedProcedure, router } from '@/trpc'
import { users } from '@/db/user'
import { eq } from 'drizzle-orm'

export const userRouter = router({
  // 获取个人信息
  user: protectedProcedure.query(async ({ ctx }) => {
    const user = await ctx.db.query.users.findFirst({
      where: eq(users.id, ctx.user.id),
    })

    if (!user) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: '用户不存在',
      })
    }

    return user
  }),

  // 更新个人信息
  update: protectedProcedure
    .input(
      z.object({
        name: z.string().min(1, '姓名不能为空').optional(),
        avatar: z.string().url('请输入有效的头像地址').optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // 只更新允许的字段
      const updateData = {
        ...(input.name && { name: input.name }),
        ...(input.avatar && { avatar: input.avatar }),
      }

      const updated = await ctx.db
        .update(users)
        .set(updateData)
        .where(eq(users.id, ctx.user.id))
        .returning()

      if (!updated[0]) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '用户不存在',
        })
      }

      return updated[0]
    }),
})