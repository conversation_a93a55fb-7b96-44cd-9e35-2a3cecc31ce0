import { postRouter } from '@/router/post'
import { authRouter } from '@/router/auth'
import { userRouter } from '@/router/user'
import { applicationRouter } from '@/router/application'
import { router } from '@/trpc'

export const appRouter = router({
  auth: authRouter,
  post: postRouter,
  user: userRouter,
  application: applicationRouter,
})

export const openApiRouter = router({
  post: postRouter,
})

// export type definition of API
export type AppRouter = typeof appRouter
