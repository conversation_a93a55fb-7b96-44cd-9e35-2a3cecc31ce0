import { pgTable } from 'drizzle-orm/pg-core'
import { createInsertSchema, createSelectSchema } from 'drizzle-zod'
import { z } from 'zod'
import { timestamps } from './columns.helpers'
import { users } from './user'

// 应用表
export const applications = pgTable('applications', (t) => ({
  id: t.uuid().notNull().primaryKey().defaultRandom(),
  name: t.varchar({ length: 10 }).notNull(),
  description: t.varchar({ length: 500 }),
  appId: t.varchar({ length: 32 }).notNull().unique(),
  secret: t.varchar({ length: 64 }).notNull(),
  balance: t.numeric({ precision: 10, scale: 2 }).default('0.00').notNull(),
  traffic: t.bigint({ mode: 'number' }).default(0).notNull(), // 流量，以KB为单位
  userId: t.uuid().notNull().references(() => users.id, { onDelete: 'cascade' }),
  ...timestamps,
}))

// 类型推导
export type Application = typeof applications.$inferSelect
export type InsertApplication = typeof applications.$inferInsert

// 应用相关的 Zod Schema
export const CreateApplicationSchema = createInsertSchema(applications, {
  name: z.string().min(1, '应用名称不能为空').max(10, '应用名称不能超过10个字符'),
  description: z.string().max(500, '描述不能超过500个字符').optional(),
}).omit({
  id: true,
  appId: true,
  secret: true,
  balance: true,
  traffic: true,
  userId: true,
  createdAt: true,
  updatedAt: true,
})

export const UpdateApplicationSchema = createInsertSchema(applications, {
  name: z.string().min(1, '应用名称不能为空').max(10, '应用名称不能超过10个字符').optional(),
  description: z.string().max(500, '描述不能超过500个字符').optional(),
}).omit({
  id: true,
  appId: true,
  secret: true,
  balance: true,
  traffic: true,
  userId: true,
  createdAt: true,
  updatedAt: true,
})

export const SelectApplicationSchema = createSelectSchema(applications)

// 应用列表查询参数
export const ApplicationListSchema = z.object({
  page: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(100).default(10),
  search: z.string().optional(),
})

export type ApplicationListParams = z.infer<typeof ApplicationListSchema>
