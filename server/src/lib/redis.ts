import Redis from 'ioredis'
import { env } from '../env'

const redis = new Redis(env.REDIS_URL!)

// 验证码数据结构
interface VerificationData {
  code: string
  attempts: number
  createdAt: number
}

// 验证码管理类
export class VerificationCodeManager {
  private static readonly PREFIX = 'verification:'
  private static readonly DEFAULT_EXPIRE = 300 // 5分钟过期
  private static readonly MAX_ATTEMPTS = 5 // 最大尝试次数

  /**
   * 生成并存储验证码
   */
  static async generateCode(
    identifier: string, 
    type: 'login' | 'register' | 'reset_password'
  ): Promise<string> {
    const code = Math.random().toString().slice(2, 8) // 生成6位随机数字
    const key = `${this.PREFIX}${type}:${identifier}`
    
    // 存储验证码信息
    const data = {
      code,
      attempts: 0,
      createdAt: Date.now()
    }
    
    await redis.setex(key, this.DEFAULT_EXPIRE, JSON.stringify(data))
    return code
  }

  /**
   * 验证验证码
   */
  static async verifyCode(
    identifier: string,
    type: 'login' | 'register' | 'reset_password',
    inputCode: string
  ): Promise<{ success: boolean; message: string }> {
    const key = `${this.PREFIX}${type}:${identifier}`
    const dataStr = await redis.get(key)
    
    if (!dataStr) {
      return { success: false, message: '验证码已过期或不存在' }
    }
    
    const data = JSON.parse(dataStr) as VerificationData
    
    // 检查尝试次数
    if (data.attempts >= this.MAX_ATTEMPTS) {
      await redis.del(key) // 删除已超过尝试次数的验证码
      return { success: false, message: '验证码尝试次数过多，请重新获取' }
    }
    
    // 验证码错误，增加尝试次数
    if (data.code !== inputCode) {
      data.attempts += 1
      await redis.setex(key, this.DEFAULT_EXPIRE, JSON.stringify(data))
      return { success: false, message: `验证码错误，还可尝试${this.MAX_ATTEMPTS - data.attempts}次` }
    }
    
    // 验证成功，删除验证码
    await redis.del(key)
    return { success: true, message: '验证成功' }
  }

  /**
   * 检查验证码发送频率限制
   */
  static async checkSendRate(identifier: string): Promise<{ canSend: boolean; remainingTime?: number }> {
    const rateKey = `${this.PREFIX}rate:${identifier}`
    const lastSent = await redis.get(rateKey)
    
    if (lastSent) {
      const elapsed = Date.now() - parseInt(lastSent)
      const waitTime = 60000 - elapsed // 1分钟间隔
      
      if (waitTime > 0) {
        return { canSend: false, remainingTime: Math.ceil(waitTime / 1000) }
      }
    }
    
    // 记录发送时间
    await redis.setex(rateKey, 60, Date.now().toString())
    return { canSend: true }
  }

  /**
   * 清除验证码
   */
  static async clearCode(
    identifier: string,
    type: 'login' | 'register' | 'reset_password'
  ): Promise<void> {
    const key = `${this.PREFIX}${type}:${identifier}`
    await redis.del(key)
  }

  /**
   * 获取验证码剩余有效时间
   */
  static async getCodeTTL(
    identifier: string,
    type: 'login' | 'register' | 'reset_password'
  ): Promise<number> {
    const key = `${this.PREFIX}${type}:${identifier}`
    return await redis.ttl(key)
  }
}

export default redis
