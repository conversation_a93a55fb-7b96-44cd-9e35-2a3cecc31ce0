/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-return */
import { sign, verify, decode } from 'jsonwebtoken'
import { env } from '../env'

const JWT_SECRET = env.JWT_SECRET
const JWT_EXPIRES_IN = '7Day'

export interface JWTPayload {
  userId: string
  phone: string
}

/**
 * 生成JWT token
 */
export function generateToken(payload: JWTPayload): string {
  console.log('JWT_SECRET', JWT_SECRET)
  return sign(payload, JWT_SECRET, {
    expiresIn: JWT_EXPIRES_IN,
  })
}

/**
 * 验证JWT token
 */
export function verifyToken(token: string): JWTPayload {
  try {
    return verify(token, JWT_SECRET) as JWTPayload
  } catch {
    throw new Error('无效的令牌')
  }
}

/**
 * 解码JWT token（不验证签名）
 */
export function decodeToken(token: string): JWTPayload | null {
  try {
    return decode(token) as JWTPayload
  } catch {
    return null
  }
}
