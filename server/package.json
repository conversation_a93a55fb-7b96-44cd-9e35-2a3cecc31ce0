{"name": "@coozf/server", "private": true, "author": "<PERSON>", "license": "ISC", "engines": {"node": ">=20.0.0"}, "scripts": {"build": "tsc && tsc-alias", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "push": "drizzle-kit push", "studio": "drizzle-kit studio --verbose", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts --cache"}, "dependencies": {"@fastify/cookie": "^11.0.2", "@fastify/cors": "^11.0.1", "@fastify/swagger": "9.4.0", "@trpc/server": "^11.4.2", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "dotenv": "^16.5.0", "drizzle-orm": "^0.44.2", "drizzle-zod": "^0.7.1", "fastify": "^5.4.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "pg": "^8.16.2", "pino": "^9.7.0", "pino-pretty": "^13.0.0", "trpc-to-openapi": "^2.3.1", "zod": "^3.25.67", "zod-openapi": "^4.2.4"}, "devDependencies": {"@eslint/compat": "^1.3.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.0.3", "@types/pg": "^8.15.4", "drizzle-kit": "^0.31.1", "eslint": "^9.29.0", "eslint-plugin-import": "^2.31.0", "trpc-ui": "^1.0.15", "tsc-alias": "^1.8.8", "tsx": "^4.20.3"}}