import { createRouter } from '@tanstack/react-router'
import { routeTree } from './routeTree.gen'
import type { AuthState } from './lib/auth/auth-context'

// 创建路由器实例
export const router = createRouter({
  routeTree,
  defaultPreload: 'intent',
  scrollRestoration: true,
  context: {
    auth: undefined! as AuthState,
  },
})

// 注册路由器类型
declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router
  }
}
