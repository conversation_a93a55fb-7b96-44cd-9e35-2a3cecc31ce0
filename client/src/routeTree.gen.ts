/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as LoginRouteImport } from './routes/login'
import { Route as AuthenticatedRouteImport } from './routes/_authenticated'
import { Route as AuthenticatedIndexRouteImport } from './routes/_authenticated/index'
import { Route as AuthenticatedProfileRouteImport } from './routes/_authenticated/profile'
import { Route as AuthenticatedAppsIndexRouteImport } from './routes/_authenticated/apps/index'
import { Route as AuthenticatedAppsCreateRouteImport } from './routes/_authenticated/apps/create'
import { Route as AuthenticatedAppsIdEditRouteImport } from './routes/_authenticated/apps/$id/edit'

const LoginRoute = LoginRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthenticatedRoute = AuthenticatedRouteImport.update({
  id: '/_authenticated',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthenticatedIndexRoute = AuthenticatedIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthenticatedRoute,
} as any)
const AuthenticatedProfileRoute = AuthenticatedProfileRouteImport.update({
  id: '/profile',
  path: '/profile',
  getParentRoute: () => AuthenticatedRoute,
} as any)
const AuthenticatedAppsIndexRoute = AuthenticatedAppsIndexRouteImport.update({
  id: '/apps/',
  path: '/apps/',
  getParentRoute: () => AuthenticatedRoute,
} as any)
const AuthenticatedAppsCreateRoute = AuthenticatedAppsCreateRouteImport.update({
  id: '/apps/create',
  path: '/apps/create',
  getParentRoute: () => AuthenticatedRoute,
} as any)
const AuthenticatedAppsIdEditRoute = AuthenticatedAppsIdEditRouteImport.update({
  id: '/apps/$id/edit',
  path: '/apps/$id/edit',
  getParentRoute: () => AuthenticatedRoute,
} as any)

export interface FileRoutesByFullPath {
  '/login': typeof LoginRoute
  '/profile': typeof AuthenticatedProfileRoute
  '/': typeof AuthenticatedIndexRoute
  '/apps/create': typeof AuthenticatedAppsCreateRoute
  '/apps': typeof AuthenticatedAppsIndexRoute
  '/apps/$id/edit': typeof AuthenticatedAppsIdEditRoute
}
export interface FileRoutesByTo {
  '/login': typeof LoginRoute
  '/profile': typeof AuthenticatedProfileRoute
  '/': typeof AuthenticatedIndexRoute
  '/apps/create': typeof AuthenticatedAppsCreateRoute
  '/apps': typeof AuthenticatedAppsIndexRoute
  '/apps/$id/edit': typeof AuthenticatedAppsIdEditRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/_authenticated': typeof AuthenticatedRouteWithChildren
  '/login': typeof LoginRoute
  '/_authenticated/profile': typeof AuthenticatedProfileRoute
  '/_authenticated/': typeof AuthenticatedIndexRoute
  '/_authenticated/apps/create': typeof AuthenticatedAppsCreateRoute
  '/_authenticated/apps/': typeof AuthenticatedAppsIndexRoute
  '/_authenticated/apps/$id/edit': typeof AuthenticatedAppsIdEditRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/login'
    | '/profile'
    | '/'
    | '/apps/create'
    | '/apps'
    | '/apps/$id/edit'
  fileRoutesByTo: FileRoutesByTo
  to: '/login' | '/profile' | '/' | '/apps/create' | '/apps' | '/apps/$id/edit'
  id:
    | '__root__'
    | '/_authenticated'
    | '/login'
    | '/_authenticated/profile'
    | '/_authenticated/'
    | '/_authenticated/apps/create'
    | '/_authenticated/apps/'
    | '/_authenticated/apps/$id/edit'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  AuthenticatedRoute: typeof AuthenticatedRouteWithChildren
  LoginRoute: typeof LoginRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authenticated': {
      id: '/_authenticated'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthenticatedRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authenticated/': {
      id: '/_authenticated/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof AuthenticatedIndexRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/profile': {
      id: '/_authenticated/profile'
      path: '/profile'
      fullPath: '/profile'
      preLoaderRoute: typeof AuthenticatedProfileRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/apps/': {
      id: '/_authenticated/apps/'
      path: '/apps'
      fullPath: '/apps'
      preLoaderRoute: typeof AuthenticatedAppsIndexRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/apps/create': {
      id: '/_authenticated/apps/create'
      path: '/apps/create'
      fullPath: '/apps/create'
      preLoaderRoute: typeof AuthenticatedAppsCreateRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/apps/$id/edit': {
      id: '/_authenticated/apps/$id/edit'
      path: '/apps/$id/edit'
      fullPath: '/apps/$id/edit'
      preLoaderRoute: typeof AuthenticatedAppsIdEditRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
  }
}

interface AuthenticatedRouteChildren {
  AuthenticatedProfileRoute: typeof AuthenticatedProfileRoute
  AuthenticatedIndexRoute: typeof AuthenticatedIndexRoute
  AuthenticatedAppsCreateRoute: typeof AuthenticatedAppsCreateRoute
  AuthenticatedAppsIndexRoute: typeof AuthenticatedAppsIndexRoute
  AuthenticatedAppsIdEditRoute: typeof AuthenticatedAppsIdEditRoute
}

const AuthenticatedRouteChildren: AuthenticatedRouteChildren = {
  AuthenticatedProfileRoute: AuthenticatedProfileRoute,
  AuthenticatedIndexRoute: AuthenticatedIndexRoute,
  AuthenticatedAppsCreateRoute: AuthenticatedAppsCreateRoute,
  AuthenticatedAppsIndexRoute: AuthenticatedAppsIndexRoute,
  AuthenticatedAppsIdEditRoute: AuthenticatedAppsIdEditRoute,
}

const AuthenticatedRouteWithChildren = AuthenticatedRoute._addFileChildren(
  AuthenticatedRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  AuthenticatedRoute: AuthenticatedRouteWithChildren,
  LoginRoute: LoginRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
