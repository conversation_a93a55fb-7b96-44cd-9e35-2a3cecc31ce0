import { Loader2 } from "lucide-react"
import { cn } from "@/lib/utils"

interface LoadingProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: number
  text?: string
}

export function Loading({ size = 24, text, className, ...props }: LoadingProps) {
  return (
    <div
      role="status"
      className={cn("flex flex-col items-center justify-center gap-2", className)}
      {...props}
    >
      <Loader2 className="animate-spin" size={size} />
      {text && <span className="text-sm text-muted-foreground">{text}</span>}
      <span className="sr-only">加载中...</span>
    </div>
  )
} 