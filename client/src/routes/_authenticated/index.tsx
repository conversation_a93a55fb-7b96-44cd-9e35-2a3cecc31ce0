import { createFileRoute } from '@tanstack/react-router'
import { useAuth } from '../../lib/auth/auth-context'
import { Card } from '../../components/ui/card'
import { Button } from '../../components/ui/button'

export const Route = createFileRoute('/_authenticated/')({
  component: HomePage,
})

function HomePage() {
  const { user, logout } = useAuth()

  const handleLogout = () => {
    logout()
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          欢迎回来！
        </h1>
        
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">用户信息</h2>
            <div className="space-y-2">
              <p className="text-sm text-gray-600">
                状态: <span className="text-green-600 font-medium">已登录</span>
              </p>
              <p className="text-sm text-gray-600">
                用户: {user ? JSON.stringify(user) : '未获取到用户信息'}
              </p>
            </div>
          </Card>

          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">快速操作</h2>
            <div className="space-y-3">
              <Button 
                variant="outline" 
                className="w-full"
                onClick={() => window.location.reload()}
              >
                刷新页面
              </Button>
              <Button 
                variant="destructive" 
                className="w-full"
                onClick={handleLogout}
              >
                退出登录
              </Button>
            </div>
          </Card>

          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">系统状态</h2>
            <div className="space-y-2">
              <p className="text-sm text-gray-600">
                认证: <span className="text-green-600">✓ 已验证</span>
              </p>
              <p className="text-sm text-gray-600">
                连接: <span className="text-green-600">✓ 正常</span>
              </p>
              <p className="text-sm text-gray-600">
                版本: <span className="text-blue-600">v1.0.0</span>
              </p>
            </div>
          </Card>
        </div>

        <div className="mt-8">
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">应用功能</h2>
            <p className="text-gray-600 mb-4">
              这是一个基于 TanStack Router 和 tRPC 的认证系统演示。
              您现在可以访问所有需要认证的功能。
            </p>
            <div className="flex flex-wrap gap-2">
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                React 19
              </span>
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                TanStack Router
              </span>
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                tRPC
              </span>
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                TypeScript
              </span>
            </div>
          </Card>
        </div>
      </div>
    </div>
  )
} 