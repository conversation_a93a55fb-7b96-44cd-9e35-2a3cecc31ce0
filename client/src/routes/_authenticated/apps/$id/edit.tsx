import { createFileRoute, useNavigate } from '@tanstack/react-router'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { ArrowLeft } from 'lucide-react'
import { trpc } from '@/lib/trpc'
import { toast } from 'sonner'
import { Link } from '@tanstack/react-router'
import { Loading } from '@/components/loading'
import { LoadingButton } from '@/components/loading-button'

const updateApplicationSchema = z.object({
  name: z.string().min(1, '应用名称不能为空').max(10, '应用名称不能超过10个字符'),
  description: z.string().max(500, '描述不能超过500个字符').optional(),
})

type UpdateApplicationForm = z.infer<typeof updateApplicationSchema>

function EditApplicationPage() {
  const { id } = Route.useParams()
  const navigate = useNavigate()

  const {
    register,
    handleSubmit,
    watch,
    reset,
    formState: { errors },
  } = useForm<UpdateApplicationForm>({
    resolver: zodResolver(updateApplicationSchema),
  })

  // 获取应用详情
  const { data: application, isLoading } = trpc.application.byId.useQuery({ id })

  // 更新应用
  const updateApplicationMutation = trpc.application.update.useMutation({
    onSuccess: () => {
      toast.success('应用更新成功')
      navigate({ to: '/apps' })
    },
    onError: (error) => {
      toast.error(error.message)
    },
  })

  // 当应用数据加载完成时，填充表单
  useEffect(() => {
    if (application) {
      reset({
        name: application.name,
        description: application.description || '',
      })
    }
  }, [application, reset])

  const onSubmit = (data: UpdateApplicationForm) => {
    updateApplicationMutation.mutate({
      id,
      ...data,
    })
  }

  const nameValue = watch('name')
  const descriptionValue = watch('description')

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" asChild>
            <Link to="/apps">
              <ArrowLeft className="w-4 h-4 mr-2" />
              返回应用列表
            </Link>
          </Button>
          <h1 className="text-3xl font-bold">编辑应用</h1>
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="text-center">加载中...</div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!application) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" asChild>
            <Link to="/apps">
              <ArrowLeft className="w-4 h-4 mr-2" />
              返回应用列表
            </Link>
          </Button>
          <h1 className="text-3xl font-bold">编辑应用</h1>
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="text-center text-muted-foreground">应用不存在</div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-4">
        <Button variant="ghost" asChild>
          <Link to="/apps">
            <ArrowLeft className="w-4 h-4 mr-2" />
            返回应用列表
          </Link>
        </Button>
        <h1 className="text-3xl font-bold">编辑应用</h1>
      </div>

      <div className="grid gap-6 lg:grid-cols-2">
        {/* 编辑表单 */}
        <Card>
          <CardHeader>
            <CardTitle>编辑应用信息</CardTitle>
            <CardDescription>
              您只能修改应用名称和描述，其他信息由系统管理
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="name">
                  应用名称 <span className="text-destructive">*</span>
                </Label>
                <Input
                  id="name"
                  {...register('name')}
                  placeholder="请输入应用名称"
                  maxLength={10}
                />
                <div className="flex justify-between text-sm text-muted-foreground">
                  <span>{errors.name?.message}</span>
                  <span>{nameValue?.length || 0}/10</span>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">应用描述</Label>
                <Textarea
                  id="description"
                  {...register('description')}
                  placeholder="请输入应用描述（可选）"
                  maxLength={500}
                  rows={4}
                />
                <div className="flex justify-between text-sm text-muted-foreground">
                  <span>{errors.description?.message}</span>
                  <span>{descriptionValue?.length || 0}/500</span>
                </div>
              </div>

              <div className="flex space-x-4">
                <Button
                  type="submit"
                  disabled={updateApplicationMutation.isPending}
                >
                  {updateApplicationMutation.isPending ? '保存中...' : '保存修改'}
                </Button>
                <Button variant="outline" asChild>
                  <Link to="/apps">取消</Link>
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>

        {/* 应用信息展示 */}
        <Card>
          <CardHeader>
            <CardTitle>应用信息</CardTitle>
            <CardDescription>
              以下信息由系统自动生成，无法修改
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label className="text-sm font-medium">应用ID</Label>
              <div className="mt-1 p-3 bg-muted rounded-md">
                <code className="text-sm font-mono">{application.appId}</code>
              </div>
            </div>

            <div>
              <Label className="text-sm font-medium">应用密钥</Label>
              <div className="mt-1 p-3 bg-muted rounded-md">
                <code className="text-sm font-mono">
                  {'*'.repeat(application.secret.length)}
                </code>
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                出于安全考虑，密钥在此处不显示
              </p>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium">余额</Label>
                <div className="mt-1 p-3 bg-muted rounded-md">
                  ¥{parseFloat(application.balance).toFixed(2)}
                </div>
              </div>
              <div>
                <Label className="text-sm font-medium">流量</Label>
                <div className="mt-1 p-3 bg-muted rounded-md">
                  {application.traffic < 1024
                    ? `${application.traffic} KB`
                    : application.traffic < 1024 * 1024
                    ? `${(application.traffic / 1024).toFixed(2)} MB`
                    : `${(application.traffic / (1024 * 1024)).toFixed(2)} GB`}
                </div>
              </div>
            </div>

            <div>
              <Label className="text-sm font-medium">创建时间</Label>
              <div className="mt-1 p-3 bg-muted rounded-md">
                {new Date(application.createdAt).toLocaleString('zh-CN')}
              </div>
            </div>

            <div>
              <Label className="text-sm font-medium">更新时间</Label>
              <div className="mt-1 p-3 bg-muted rounded-md">
                {new Date(application.updatedAt).toLocaleString('zh-CN')}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export const Route = createFileRoute('/_authenticated/apps/$id/edit')({
  component: EditApplicationPage,
})
