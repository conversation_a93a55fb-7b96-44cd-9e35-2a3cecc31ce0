import { createFileRoute } from '@tanstack/react-router'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { ArrowLeft, Copy, Eye, EyeOff } from 'lucide-react'
import { trpc } from '@/lib/trpc'
import { toast } from 'sonner'
import { Link } from '@tanstack/react-router'
import { useState } from 'react'
import { LoadingButton } from '@/components/loading-button'
import type { Application } from '@server/db/schema'

const createApplicationSchema = z.object({
  name: z.string().min(1, '应用名称不能为空').max(10, '应用名称不能超过10个字符'),
  description: z.string().max(500, '描述不能超过500个字符').optional(),
})

type CreateApplicationForm = z.infer<typeof createApplicationSchema>

function CreateApplicationPage() {
  const [createdApp, setCreatedApp] = useState<Application | null>(null)
  const [showSecret, setShowSecret] = useState(false)

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<CreateApplicationForm>({
    resolver: zodResolver(createApplicationSchema),
    defaultValues: {
      name: '',
      description: '',
    },
  })

  const createApplicationMutation = trpc.application.create.useMutation({
    onSuccess: (data) => {
      toast.success('应用创建成功')
      setCreatedApp(data)
    },
    onError: (error) => {
      toast.error(error.message)
    },
  })

  const onSubmit = (data: CreateApplicationForm) => {
    createApplicationMutation.mutate(data)
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast.success('已复制到剪贴板')
  }

  const nameValue = watch('name')
  const descriptionValue = watch('description')

  // 如果应用已创建，显示成功页面
  if (createdApp) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" asChild>
            <Link to="/apps">
              <ArrowLeft className="w-4 h-4 mr-2" />
              返回应用列表
            </Link>
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="text-green-600">应用创建成功！</CardTitle>
            <CardDescription>
              您的应用已成功创建，请妥善保存以下信息
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid gap-4">
              <div>
                <Label>应用名称</Label>
                <div className="mt-1 p-3 bg-muted rounded-md">
                  {createdApp.name}
                </div>
              </div>

              {createdApp.description && (
                <div>
                  <Label>应用描述</Label>
                  <div className="mt-1 p-3 bg-muted rounded-md">
                    {createdApp.description}
                  </div>
                </div>
              )}

              <div>
                <Label>应用ID</Label>
                <div className="mt-1 flex items-center space-x-2">
                  <code className="flex-1 p-3 bg-muted rounded-md font-mono text-sm">
                    {createdApp.appId}
                  </code>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(createdApp.appId)}
                  >
                    <Copy className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              <div>
                <Label>应用密钥</Label>
                <div className="mt-1 flex items-center space-x-2">
                  <code className="flex-1 p-3 bg-muted rounded-md font-mono text-sm">
                    {showSecret ? createdApp.secret : '*'.repeat(createdApp.secret.length)}
                  </code>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowSecret(!showSecret)}
                  >
                    {showSecret ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </Button>
                  {showSecret && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyToClipboard(createdApp.secret)}
                    >
                      <Copy className="w-4 h-4" />
                    </Button>
                  )}
                </div>
                <p className="text-sm text-muted-foreground mt-1">
                  请妥善保存您的密钥，它将用于API调用认证
                </p>
              </div>
            </div>

            <div className="flex space-x-4">
              <Button asChild>
                <Link to="/apps">
                  返回应用列表
                </Link>
              </Button>
              <Button variant="outline" onClick={() => setCreatedApp(null)}>
                创建另一个应用
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-4">
        <Button variant="ghost" asChild>
          <Link to="/apps">
            <ArrowLeft className="w-4 h-4 mr-2" />
            返回应用列表
          </Link>
        </Button>
        <h1 className="text-3xl font-bold">创建应用</h1>
      </div>

      <Card className="max-w-2xl">
        <CardHeader>
          <CardTitle>新建应用</CardTitle>
          <CardDescription>
            创建一个新的应用程序，系统将自动生成应用ID和密钥
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="name">
                应用名称 <span className="text-destructive">*</span>
              </Label>
              <Input
                id="name"
                {...register('name')}
                placeholder="请输入应用名称"
                maxLength={10}
              />
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>{errors.name?.message}</span>
                <span>{nameValue?.length || 0}/10</span>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">应用描述</Label>
              <Textarea
                id="description"
                {...register('description')}
                placeholder="请输入应用描述（可选）"
                maxLength={500}
                rows={4}
              />
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>{errors.description?.message}</span>
                <span>{descriptionValue?.length || 0}/500</span>
              </div>
            </div>

            <div className="bg-muted p-4 rounded-lg">
              <h4 className="font-medium mb-2">系统将自动生成：</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• 应用ID：用于标识您的应用程序</li>
                <li>• 应用密钥：用于API调用认证</li>
                <li>• 初始余额：¥0.00</li>
                <li>• 初始流量：0 KB</li>
              </ul>
            </div>

            <div className="flex space-x-4">
              <LoadingButton
                type="submit"
                loading={createApplicationMutation.isPending}
                loadingText="创建中..."
              >
                创建应用
              </LoadingButton>
              <Button variant="outline" asChild>
                <Link to="/apps">取消</Link>
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}

export const Route = createFileRoute('/_authenticated/apps/create')({
  component: CreateApplicationPage,
})
