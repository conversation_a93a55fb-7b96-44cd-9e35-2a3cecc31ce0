import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { RouterProvider } from '@tanstack/react-router'
import { trpc } from './lib/trpc'
import { getAuthToken } from './lib/auth/auth'
import { AuthProvider } from './lib/auth/auth-provider'
import { useAuth } from './lib/auth/auth-context'
import { router } from './router'
import './App.css'
import { useState } from 'react'
import { httpBatchLink } from '@trpc/client'
import superjson from 'superjson'
import { ThemeProvider } from '@/components/theme-provider'
import { Toaster } from 'sonner'
import { toast } from 'sonner'
import { TRPCClientError } from '@trpc/client'
import type { AppRouter } from '@server/router'

// 统一的错误处理函数
const handleError = (error: TRPCClientError<AppRouter>) => {
  // 可以在这里添加错误日志上报
  console.error('API Error:', error)

  // 处理特定类型的错误
  if (error instanceof TRPCClientError) {
    if (error.data?.httpStatus === 401) {
      toast.error('登录已过期，请重新登录')
      // 可以在这里触发登出逻辑
      return
    }

    if (error.message.includes('NETWORK_ERROR')) {
      toast.error('网络连接错误，请检查网络后重试')
      return
    }
  }

  // 默认错误提示
  toast.error(error.message || '操作失败，请稍后重试')
}

function InnerApp() {
  const auth = useAuth()
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            refetchOnWindowFocus: false,
            retry: (failureCount, error) => {
              // 对于认证错误和参数错误不进行重试
              if (error instanceof TRPCClientError) {
                if (
                  error.data.httpStatus === 401 ||
                  error.data.code.includes('UNAUTHORIZED') ||
                  error.data.code.includes('INVALID_INPUT') ||
                  error.data.code.includes('INVALID_PARAMS')
                ) {
                  auth.logout()
                  return false
                }
              }
              // 其他错误最多重试2次
              return failureCount < 2
            },
          },
          mutations: {
            retry: false, // 默认不重试变更操作
            onError: (error) => {
              handleError(error as TRPCClientError<AppRouter>)
            },
          },
        },
      })
  )
  const [trpcClient] = useState(() =>
    trpc.createClient({
      links: [
        httpBatchLink({
          url: import.meta.env.VITE_API_BASE_URL + '/api/trpc',
          async headers() {
            // 从 localStorage 获取 token
            const token = getAuthToken()
            return {
              authorization: token ? `Bearer ${token}` : '',
            }
          },
          transformer: superjson,
        }),
      ],
    })
  )

  return (
    <trpc.Provider client={trpcClient} queryClient={queryClient}>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider defaultTheme="light" storageKey="vite-ui-theme">
          <RouterProvider router={router} context={{ auth }} />
          <Toaster position="top-right" richColors />
        </ThemeProvider>
      </QueryClientProvider>
    </trpc.Provider>
  )
}

function App() {
  return (
    <AuthProvider>
      <InnerApp />
    </AuthProvider>
  )
}

export default App
