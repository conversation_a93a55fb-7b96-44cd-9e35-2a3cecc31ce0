---
description: 
globs: 
alwaysApply: false
---
# 数据库和 Drizzle ORM 规范

## Schema 定义
- 所有数据库 schema 定义在 [server/src/db/schema.ts](mdc:server/src/db/schema.ts) 文件中
- 使用 Drizzle ORM 的 PostgreSQL 适配器
- 表名使用复数形式（如 `users`，`posts`）

```typescript
// ✅ 正确示例 - 参考 [server/src/db/schema.ts](mdc:server/src/db/schema.ts)
export const user = pgTable("users", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  name: varchar({ length: 255 }).notNull(),
  age: integer().notNull(),
  email: varchar({ length: 255 }).notNull().unique(),
})
```

## 字段约束
- 为所有字段定义适当的约束
- 使用 `notNull()` 标记必填字段
- 为字符串字段设置长度限制
- 为唯一字段添加 `unique()` 约束

## 主键和外键
- 优先使用自增整数主键（`generatedAlwaysAsIdentity()`）
- 对于需要UUID的场景，使用 `uuid().defaultRandom()`
- 正确定义外键关系

## 时间戳字段
- 使用 `timestamp()` 类型处理时间
- 设置适当的默认值：`defaultNow()`
- 使用 `$onUpdateFn` 处理更新时间

```typescript
// ✅ 正确示例
createdAt: t.timestamp().defaultNow().notNull(),
updatedAt: t.timestamp({ mode: "date", withTimezone: true }).$onUpdateFn(() => sql`now()`)
```

## Zod 集成
- 使用 `drizzle-zod` 创建验证 schema
- 使用 `createInsertSchema` 生成插入验证
- 适当地省略自动生成的字段（如 `id`、`createdAt`）

```typescript
// ✅ 正确示例 - 参考 [server/src/db/schema.ts](mdc:server/src/db/schema.ts)
export const CreatePostSchema = createInsertSchema(post, {
  title: (schema) => schema.max(256),
  content: (schema) => schema.max(256),
}).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
})
```

## 查询规范
- 使用 `ctx.db.query` 进行类型安全的查询
- 为查询添加适当的限制和排序
- 使用 Drizzle 的操作符进行条件查询

```typescript
// ✅ 正确示例
return ctx.db.query.user.findMany({
  orderBy: desc(user.id),
  limit: 10,
})
```

## 数据库连接
- 数据库连接配置在 [server/src/db/index.ts](mdc:server/src/db/index.ts) 中
- 使用环境变量管理数据库连接信息
- 正确配置连接池参数
